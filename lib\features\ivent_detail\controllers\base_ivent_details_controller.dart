import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

abstract class BaseIventDetailsController extends BaseController {
  final IventDetailStateManager state;

  final String iventId;

  BaseIventDetailsController(
    AuthService authService,
    this.state,
    this.iventId,
  ) : super(authService);
}
