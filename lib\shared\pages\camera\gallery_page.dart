import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/layout/navigation/ia_top_bar.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:photo_manager/photo_manager.dart' as photo_manager;
import 'package:photo_manager/photo_manager.dart';

class GalleryPage extends StatefulWidget {
  final VibeUploadController controller;

  const GalleryPage({super.key, required this.controller});

  @override
  State<GalleryPage> createState() => _GalleryPageState();
}

class _GalleryPageState extends State<GalleryPage> with TickerProviderStateMixin {
  VibeUploadController get _controller => widget.controller;

  bool _isLoading = true;
  bool _inMultiSelectMode = false;
  List<AssetEntity> _assets = [];
  String? _capturedImagePath;
  List<String> _capturedImagePaths = [];
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAssets();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAssets() async {
    final permission = await Permission.photos.request();
    if (permission.isDenied) return;

    final List<AssetEntity> assets = await PhotoManager.getAssetListPaged(
      page: 0,
      pageCount: 30,
      type: photo_manager.RequestType.image,
    );

    setState(() {
      _assets = assets;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.empty(
      // appBar: IaTopBar.searchTopBar(
      //   padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
      //   backbuttonColor: AppColors.black,
      //   trailing: GestureDetector(
      //     onTap: () => setState(() {
      //       _inMultiSelectMode = !_inMultiSelectMode;
      //       if (!_inMultiSelectMode) {
      //         _capturedImagePaths.clear();
      //       }
      //     }),
      //     child: Container(
      //       child: Center(
      //         child: Text(
      //           _inMultiSelectMode ? 'Vazgeç' : 'Çoklu Seçim',
      //           style: AppTextStyles.size14Bold,
      //         ),
      //       ),
      //     ),
      //   ),
      // ),
      body: Stack(
        children: [
          _buildBody(),
          if (_inMultiSelectMode) _buildConfirmationBox(),
        ],
      ),
    );
  }

  Align _buildConfirmationBox() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: double.infinity,
        height: 80,
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
          color: AppColors.primary,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected ${_capturedImagePaths.length} images',
              style: AppTextStyles.size14Bold,
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  _inMultiSelectMode = false;
                  _capturedImagePaths.clear();
                });
                Get.back(result: _capturedImagePaths);
              },
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.local_movies_rounded),
                    const SizedBox(width: 10),
                    Text(
                      'Memories Ekle',
                      style: AppTextStyles.size14Bold,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return DefaultTabController(
      length: 2,
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverToBoxAdapter(
              child: _buildTabBar(),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildGalleryTab(),
            _buildGalleryTab(),
          ],
        ),
      ),
    );
  }

  SizedBox _buildTabBar() {
    return SizedBox(
      height: 40,
      child: TabBar(
        controller: _tabController,
        dividerHeight: 2,
        indicatorColor: AppColors.primary,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: [
          Tab(
            child: Text(
              'Camera Roll',
              style: _tabController.index == 0 ? AppTextStyles.size14Bold : AppTextStyles.size14Bold,
            ),
          ),
          Tab(
            child: Text(
              'iVent Memories',
              style: _tabController.index == 1 ? AppTextStyles.size14Bold : AppTextStyles.size14Bold,
            ),
          ),
        ],
      ),
    );
  }

  Container _buildGalleryTab() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        padding: const EdgeInsets.symmetric(vertical: 20),
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.6,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
        itemCount: _assets.length,
        itemBuilder: (context, index) {
          final element = _assets[index];
          return _buildImageGridItem(element);
        },
      ),
    );
  }

  GestureDetector _buildImageGridItem(AssetEntity element) {
    return GestureDetector(
      onTap: () => _handleImageTap(element),
      onLongPress: () => _handleImageLongPress(element),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Stack(
          children: [
            _buildImageItem(element),
            if (_inMultiSelectMode) _buildSelectionButton(element),
          ],
        ),
      ),
    );
  }

  Positioned _buildSelectionButton(AssetEntity element) {
    final bool isSelected = _capturedImagePaths.contains(element.id);

    return Positioned(
      right: 10,
      bottom: 10,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.transparent,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? AppColors.transparent : AppColors.primary,
            width: 2,
          ),
        ),
        child: isSelected
            ? const Icon(
                Icons.check,
                color: AppColors.white,
                size: 16,
              )
            : null,
      ),
    );
  }

  Future<void> _handleImageTap(AssetEntity element) async {
    if (_inMultiSelectMode) {
      _toggleImageSelection(element);
      return;
    }

    final file = await element.file;
    _controller.capturedImagePath = file?.path ?? '';
    Get.back();
  }

  void _handleImageLongPress(AssetEntity element) {
    setState(() {
      _inMultiSelectMode = true;
      _capturedImagePaths.add(element.id);
    });
  }

  void _toggleImageSelection(AssetEntity element) {
    setState(() {
      if (_capturedImagePaths.contains(element.id)) {
        _capturedImagePaths.remove(element.id);
        if (_capturedImagePaths.isEmpty) {
          _inMultiSelectMode = false;
        }
      } else {
        _capturedImagePaths.add(element.id);
      }
    });
  }

  FutureBuilder<Uint8List?> _buildImageItem(AssetEntity element) {
    return FutureBuilder<Uint8List?>(
      future: element.thumbnailData,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
          // return
        }
        return const SizedBox(
          child: Center(
            child: CircularProgressIndicator(strokeWidth: 2.0),
          ),
        );
      },
    );
  }
}
