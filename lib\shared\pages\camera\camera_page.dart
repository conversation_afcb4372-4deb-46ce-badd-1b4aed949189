import 'dart:io';
import 'dart:math';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/pages/vibe_upload.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraPage extends StatefulWidget {
  const CameraPage({super.key});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> with WidgetsBindingObserver {
  VibeUploadController _vibeUploadController = Get.put(VibeUploadController(
    Get.find()
  ));

  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isLoading = true;
  bool _isRecording = false;
  bool _isFlashOn = false;
  CameraLensDirection _direction = CameraLensDirection.front;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initCamera();
  }

  @override
  void dispose() {
    _controller?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }
    if (state == AppLifecycleState.inactive) {
      _controller?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      if (_controller != null) {
        _onNewCameraSelected(_controller!.description);
      }
    }
  }

  Future<void> _initCamera() async {
    // Request permissions
    await [Permission.camera, Permission.storage].request();

    try {
      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        final frontCamera = _cameras!.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.front,
          orElse: () => _cameras!.first,
        );
        await _onNewCameraSelected(frontCamera);
      }
    } catch (e) {
      print(e);
    }

    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _onNewCameraSelected(CameraDescription description) async {
    if (_controller != null) {
      await _controller!.dispose();
    }

    _controller = CameraController(
      description,
      ResolutionPreset.medium,
      enableAudio: true,
    );

    try {
      await _controller!.initialize();
    } catch (e) {
      print(e);
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }

    try {
      final Directory extDir = await getTemporaryDirectory();
      final String dirPath = '${extDir.path}/Pictures/flutter_test';
      await Directory(dirPath).create(recursive: true);
      final String filePath = '$dirPath/${DateTime.now().millisecondsSinceEpoch}.jpg';

      if (_controller!.value.isTakingPicture) {
        return;
      }

      final XFile picture = await _controller!.takePicture();
      await picture.saveTo(filePath);
      _vibeUploadController.capturedImagePath = filePath;
      Get.to(VibeUpload());
    } catch (e) {
      print(e);
    }
  }

  Future<void> _toggleRecording() async {
    if (_isRecording) {
      final XFile? file = await _controller!.stopVideoRecording();
      setState(() => _isRecording = false);
      if (file != null) {
        // Handle the recorded video file
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Video saved to ${file.path}')),
        );
      }
    } else {
      try {
        final Directory extDir = await getTemporaryDirectory();
        final String dirPath = '${extDir.path}/Movies/flutter_test';
        await Directory(dirPath).create(recursive: true);
        final String filePath = '$dirPath/${DateTime.now().millisecondsSinceEpoch}.mp4';

        await _controller!.startVideoRecording();
        setState(() => _isRecording = true);
      } catch (e) {
        print(e);
      }
    }
  }

  Future<void> _toggleFlash() async {
    if (_controller == null || !_controller!.value.isInitialized) return;
    if (_direction != CameraLensDirection.back) return;

    try {
      await _controller!.setFlashMode(
        _isFlashOn ? FlashMode.off : FlashMode.torch,
      );
      setState(() => _isFlashOn = !_isFlashOn);
    } catch (e) {
      print(e);
    }
  }

  Future<void> _switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) return;

    final newDirection = _direction == CameraLensDirection.back ? CameraLensDirection.front : CameraLensDirection.back;

    final newCamera = _cameras!.firstWhere(
      (camera) => camera.lensDirection == newDirection,
    );
    _vibeUploadController.isFrontCamera = newDirection == CameraLensDirection.front;

    _direction = newDirection;
    await _onNewCameraSelected(newCamera);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return IaScaffold.empty(
      body: Stack(
        children: [
          _buildCamera(),
          _buildCameraOverlay(),
        ],
      ),
    );
  }

  Positioned _buildCamera() {
    final isFrontCamera = _direction == CameraLensDirection.front;

    return Positioned.fill(
      child: Transform(
        alignment: Alignment.center,
        transform: Matrix4.rotationY(isFrontCamera ? pi : 0),
        child: Container(
          color: AppColors.black,
          child: Center(
            child: AspectRatio(
              aspectRatio: 1 / _controller!.value.aspectRatio,
              child: CameraPreview(_controller!),
            ),
          ),
        ),
      ),
    );
  }

  Positioned _buildCameraOverlay() {
    return Positioned.fill(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
        child: Stack(
          children: [
            const Align(
              alignment: Alignment.topLeft,
              // child: IaBackButton(),
            ),
            if (_direction == CameraLensDirection.back)
              const Align(
                alignment: Alignment.topCenter,
                // child: IaCircleIconButton(
                //   iconPath: _isFlashOn ? AppAssets.flashOn : AppAssets.flashOff,
                //   iconColor: AppColors.white,
                //   backgroundColor: AppColors.transparent,
                //   size: 20,
                //   onPressed: _toggleFlash,
                // ),
              ),
            const Align(
              alignment: Alignment.bottomLeft,
              // child: IaCircleIconButton(
              //   iconPath: AppAssets.cameraGallery,
              //   iconColor: AppColors.white,
              //   size: 25,
              //   onPressed: () => Get.to(GalleryPage(controller: _vibeUploadController)),
              // ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: _buildPhotoCaptureButton(),
            ),
            const Align(
              alignment: Alignment.bottomRight,
              // child: IaCircleIconButton(
              //   iconPath: AppAssets.camera1,
              //   iconColor: AppColors.white,
              //   size: 25,
              //   onPressed: _switchCamera,
              // ),
            ),
          ],
        ),
      ),
    );
  }

  GestureDetector _buildPhotoCaptureButton() {
    return GestureDetector(
      onTap: _takePicture,
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          color: AppColors.transparent,
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColors.white,
            width: 4,
          ),
        ),
        child: Center(
          child: Container(
            height: 44,
            decoration: const BoxDecoration(
              color: AppColors.white,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ),
    );
  }
}
