import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class ProfileButtons {
  ProfileButtons._();

  static IaRoundedButton _profile({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isActive,
    required String text,
    required String iconPath,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      height: AppDimensions.buttonHeightProfile,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isActive ? AppColors.primary : AppColors.grey400,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium.copyWith(color: isActive ? AppColors.white : AppColors.grey600),
      leading: IaSvgIcon(iconPath: iconPath),
    );
  }

  static IaRoundedButton profileFollowings({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _profile(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: true,
      text: 'Takip Ettiklerin',
      iconPath: AppAssets.wavyCheck,
    );
  }

  static IaRoundedButton profileFavorites({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _profile(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: true,
      text: 'Favoriler',
      iconPath: AppAssets.star,
    );
  }

  static IaRoundedButton profileAddFriend({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isFriend,
  }) {
    return _profile(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: !isFriend,
      text: isFriend ? 'Arkadaşsınız' : 'Arkadaş Ekle',
      iconPath: isFriend ? AppAssets.users : AppAssets.userAdd,
    );
  }

  static IaRoundedButton profileFollow({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isFollowing,
  }) {
    return _profile(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: !isFollowing,
      text: isFollowing ? 'Takip Ediyorsun' : 'Takip Et',
      iconPath: isFollowing ? AppAssets.check : AppAssets.addPlus,
    );
  }

  static IaRoundedButton profileMembers({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _profile(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: true,
      text: 'Üyeler',
      iconPath: AppAssets.usersGroup,
    );
  }

  static IaRoundedButton profileTag({
    Key? key,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    required String text,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightProfileTag,
      roundness: AppDimensions.buttonRadiusL,
      onTap: onTap,
      color: AppColors.lightGrey,
      text: text,
      textStyle: AppTextStyles.size12Medium,
    );
  }

  static IaCircularButton profileTagEditButton({
    Key? key,
  }) {
    return IaCircularButton(
      key: key,
      buttonSize: AppDimensions.buttonSizeProfileTagEdit,
      backgroundColor: AppColors.primary,
      iconPath: AppAssets.editPencil02,
      iconSize: AppDimensions.buttonSizeProfileTagEdit,
    );
  }

  static IaCircularButton createVibe({
    Key? key,
  }) {
    return SharedButtons.createIventButton(key: key, buttonSize: AppDimensions.buttonSizecreateVibe);
  }
}
