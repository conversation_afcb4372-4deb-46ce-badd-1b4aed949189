//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class VibesApi {
  VibesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Vibe oluşturulur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreateVibeDto] createVibeDto (required):
  Future<Response> createVibeWithHttpInfo(CreateVibeDto createVibeDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/create';

    // ignore: prefer_final_locals
    Object? postBody = createVibeDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe oluşturulur
  ///
  /// Parameters:
  ///
  /// * [CreateVibeDto] createVibeDto (required):
  Future<CreateVibeReturn?> createVibe(CreateVibeDto createVibeDto,) async {
    final response = await createVibeWithHttpInfo(createVibeDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateVibeReturn',) as CreateVibeReturn;
    
    }
    return null;
  }

  /// Vibe IDsi ile vibe silinir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deleteByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe silinir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deleteByVibeId(String id,) async {
    final response = await deleteByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Vibe IDsi ile vibe getirilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe getirilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetVibeByVibeIdReturn?> getByVibeId(String id,) async {
    final response = await getByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetVibeByVibeIdReturn',) as GetVibeByVibeIdReturn;
    
    }
    return null;
  }

  /// Vibe IDsi ile vibe yorumları listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getCommentsByVibeIdWithHttpInfo(String id, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/comments'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe yorumları listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetCommentsByVibeIdReturn?> getCommentsByVibeId(String id, { int? limit, int? page, }) async {
    final response = await getCommentsByVibeIdWithHttpInfo(id,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetCommentsByVibeIdReturn',) as GetCommentsByVibeIdReturn;
    
    }
    return null;
  }

  /// Vibe IDsi ile vibe beğenileri listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getLikesByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/likes'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe beğenileri listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetLikesByVibeIdReturn?> getLikesByVibeId(String id,) async {
    final response = await getLikesByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetLikesByVibeIdReturn',) as GetLikesByVibeIdReturn;
    
    }
    return null;
  }

  /// Vibe sekmesi listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getVibesWithHttpInfo({ int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe sekmesi listelenir
  ///
  /// Parameters:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetVibesReturn?> getVibes({ int? limit, int? page, }) async {
    final response = await getVibesWithHttpInfo( limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetVibesReturn',) as GetVibesReturn;
    
    }
    return null;
  }

  /// Vibe IDsi ile vibe gizlenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> hideByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/hide'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe gizlenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> hideByVibeId(String id,) async {
    final response = await hideByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Vibe IDsi ile vibe beğenilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> likeByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/likes/like'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe beğenilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> likeByVibeId(String id,) async {
    final response = await likeByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Vibe IDsi ile vibe gösterime girer
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> showByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/show'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe gösterime girer
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> showByVibeId(String id,) async {
    final response = await showByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Vibe IDsi ile vibe beğenisi kaldırılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unlikeByVibeIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/likes/unlike'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe beğenisi kaldırılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unlikeByVibeId(String id,) async {
    final response = await unlikeByVibeIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Vibe IDsi ile vibe bilgileri güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateByVibeIdDto] updateByVibeIdDto (required):
  Future<Response> updateByVibeIdWithHttpInfo(String id, UpdateByVibeIdDto updateByVibeIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/vibes/{id}/update'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateByVibeIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Vibe IDsi ile vibe bilgileri güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateByVibeIdDto] updateByVibeIdDto (required):
  Future<void> updateByVibeId(String id, UpdateByVibeIdDto updateByVibeIdDto,) async {
    final response = await updateByVibeIdWithHttpInfo(id, updateByVibeIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

