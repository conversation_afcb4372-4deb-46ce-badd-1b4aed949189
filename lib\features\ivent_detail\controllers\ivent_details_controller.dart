import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/collabs_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/invitations_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_info_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/participants_controller.dart';

class IventDetailsController extends BaseIventDetailsController {
  IventDetailsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  late final IventInfoController iventInfoController;
  late final ParticipantsController participantsController;
  late final InvitationsController invitationsController;
  late final CollabsController collabsController;

  @override
  void initController() async {
    super.initController();

    iventInfoController = Get.put(
      IventInfoController(authService, state, iventId),
      tag: iventId,
    );
    participantsController = Get.put(
      ParticipantsController(authService, state, iventId),
      tag: iventId,
    );
    invitationsController = Get.put(
      InvitationsController(authService, state, iventId),
      tag: iventId,
    );
    collabsController = Get.put(
      CollabsController(authService, state, iventId),
      tag: iventId,
    );
  }

  @override
  void closeController() {
    Get.delete<IventInfoController>(tag: iventId);
    Get.delete<ParticipantsController>(tag: iventId);
    Get.delete<InvitationsController>(tag: iventId);
    Get.delete<CollabsController>(tag: iventId);
    super.closeController();
  }

  Future<void> goToInitiallyInvitableUsersPage() async => invitationsController.getInitiallyInvitableUsersPage();

  Future<void> goToInviteMoreUsersPage() async => invitationsController.getInviteMoreUsersPage();

  Future<void> goToParticipantsPage(
    IventViewTypeEnum viewType,
    int participantCount,
  ) async =>
      participantsController.getParticipantsPage(viewType, participantCount);

  Future<void> goToCollabsPage() async => collabsController.getCollabsPage();
}
