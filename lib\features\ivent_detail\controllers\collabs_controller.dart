import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/ivent_detail.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class CollabsController extends BaseIventDetailsController {
  CollabsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  late final BaseSearchBarController baseSearchBarController;

  final _collabsResult = Rxn<SearchCollabsReturn>();

  SearchCollabsReturn? get collabsResult => _collabsResult.value;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => collabsResult?.collabs.isEmpty ?? true;

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController = Get.put(BaseSearchBarController((q) => _searchCollabs(q: q)), tag: 'CollabsController');
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'CollabsController');
    super.closeController();
  }

  Future<void> _searchCollabs({String? q}) async {
    _collabsResult.value = await authService.iventCollabsApi.searchCollabs(
      state.iventId,
      q: q,
    );
  }

  Future<void> getCollabsPage({String? q}) async {
    Get.toNamed(IventDetayRoutes.IVENT_DETAY_PAYDASLAR, arguments: state.iventId);
    if (collabsResult == null) await _searchCollabs();
  }
}
