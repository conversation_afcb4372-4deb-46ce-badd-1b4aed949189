import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/utils/map_utils.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class FeedController extends BaseHomeController {
  final HomePanelsController homePanelsController;

  FeedController(
    AuthService authService,
    HomeStateManager state,
    this.homePanelsController,
  ) : super(authService, state);

  final dateFilters = FeedDateEnum.values;

  final _iventItems = <IventCardItem>[].obs;
  final _existingFeedParams = Rx<Map<String, dynamic>>({});
  final _feedPage = 1.obs;
  final _feedContinuable = true.obs;
  final _isSearching = false.obs;

  List<IventCardItem> get iventItems => _iventItems;
  Map<String, dynamic> get existingFeedParams => _existingFeedParams.value;
  int get feedPage => _feedPage.value;
  bool get feedContinuable => _feedContinuable.value;
  bool get isSearching => _isSearching.value;

  set iventItems(List<IventCardItem> value) => _iventItems.value = value;
  set existingFeedParams(Map<String, dynamic> value) => _existingFeedParams.value = value;
  set feedPage(int value) => _feedPage.value = value;
  set feedContinuable(bool value) => _feedContinuable.value = value;
  set isSearching(bool value) => _isSearching.value = value;

  @override
  void initController() async {
    super.initController();
    await retrieveIventList();
  }

  Future<void> retrieveIventList() async {
    final updatedFeedParams = {
      'dateType': dateFilters[state.dateFilterIndex],
      'categories': state.selectedHobbyIds.join(','),
      'locationCoeff': state.locationCoeff,
    };

    if (state.selectedPlace != null) {
      final rangeDegrees = state.locationCoeff * 0.01;
      updatedFeedParams['latStart'] = state.selectedPlace!.latitude - rangeDegrees;
      updatedFeedParams['latEnd'] = state.selectedPlace!.latitude + rangeDegrees;
      updatedFeedParams['lngStart'] = state.selectedPlace!.longitude - rangeDegrees;
      updatedFeedParams['lngEnd'] = state.selectedPlace!.longitude + rangeDegrees;
    }

    if (state.dateFilterIndex == 0 && state.selectedDates.length == 2) {
      updatedFeedParams['startDate'] = DateFormat('y-M-dT00:00:00.000Z').format(state.selectedDates[0]);
      updatedFeedParams['endDate'] = DateFormat('y-M-dT23:59:59.999Z').format(state.selectedDates[1]);
    }

    if (!compareMaps(existingFeedParams, updatedFeedParams)) {
      existingFeedParams = updatedFeedParams;

      isSearching = true;
      final result = await authService.homeApi.feed(
        existingFeedParams['dateType'],
        existingFeedParams['categories'],
        locationCoeff: existingFeedParams['locationCoeff'],
        latStart: existingFeedParams['latStart'],
        latEnd: existingFeedParams['latEnd'],
        lngStart: existingFeedParams['lngStart'],
        lngEnd: existingFeedParams['lngEnd'],
        startDate: existingFeedParams['startDate'],
        endDate: existingFeedParams['endDate'],
        page: 1,
      );
      isSearching = false;

      if (result != null) {
        iventItems = result.ivents;
        feedPage = 1;
        feedContinuable = true;
      }
    }
  }

  Future<void> loadNewIventItems() async {
    if (feedContinuable) {
      final result = await authService.homeApi.feed(
        existingFeedParams['dateType'],
        existingFeedParams['categories'],
        locationCoeff: existingFeedParams['locationCoeff'],
        latStart: existingFeedParams['latStart'],
        latEnd: existingFeedParams['latEnd'],
        lngStart: existingFeedParams['lngStart'],
        lngEnd: existingFeedParams['lngEnd'],
        startDate: existingFeedParams['startDate'],
        endDate: existingFeedParams['endDate'],
        page: feedPage + 1,
      );

      if (result != null) {
        iventItems.addAll(result.ivents);
        feedPage++;
      } else {
        feedContinuable = false;
      }
    }
  }

  void toggleTimeFilter(int index) {
    state.dateFilterIndex = index;
    if (index != 0) state.selectedDates = [];
  }

  void toggleSelectedDates(DateTime startDate, DateTime endDate) {
    state.selectedDates = [startDate, endDate];
  }

  String selectedDatesAsString() {
    if (state.selectedDates.isEmpty) return 'Start Date';
    if (state.selectedDates.length == 1) return 'End Date';
    final String startDate = DateFormat('d MMM').format(state.selectedDates[0]);
    final String endDate = DateFormat('d MMM').format(state.selectedDates[1]);
    return '$startDate - $endDate';
  }

  void applyFilters() {
    retrieveIventList();
    homePanelsController.goToFeedPage();
  }
}
