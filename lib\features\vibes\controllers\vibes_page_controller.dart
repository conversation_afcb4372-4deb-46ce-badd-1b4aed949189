import 'dart:math';

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';
import 'package:ivent_app/features/vibes/models/vibe_folder.dart';

class VibesPageController extends BaseVibesController {
  VibesPageController(AuthService authService) : super(authService);

  final _vibeFolders = <VibeFolder>[].obs;
  final _currentVibePage = 0.obs;
  final _isScrollContinuable = true.obs;
  final _isLoadingMore = false.obs;

  List<VibeFolder> get vibeFolders => _vibeFolders;
  int get currentVibePage => _currentVibePage.value;
  bool get isScrollContinuable => _isScrollContinuable.value;
  bool get isLoadingMore => _isLoadingMore.value;

  VibeFolder get currentVibeFolder => vibeFolders[currentVibePage];
  Map<int, Vibe> get currentVibelets => currentVibeFolder.vibelets;
  Vibe get currentVibelet => currentVibeFolder.currentVibelet;
  int get currentVibeletIndex => currentVibeFolder.currentVibeletIndex;

  set vibeFolders(List<VibeFolder> value) => _vibeFolders.assignAll(value);
  set currentVibePage(int value) => _currentVibePage.value = value;
  set isScrollContinuable(bool value) => _isScrollContinuable.value = value;
  set isLoadingMore(bool value) => _isLoadingMore.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await _loadInitialVibes();
    print('VibesPageController has been initialized with user: ${sessionUser.sessionId}');
  }

  Future<void> _loadInitialVibes() async {
    final initialVibeFolders = await authService.vibesApi.getVibes(limit: 6, page: 1);
    if (initialVibeFolders == null) return;
    vibeFolders = initialVibeFolders.vibes
        .map((val) => VibeFolder(
              vibeFolderId: val.vibeFolderId,
              thumbnailUrl: val.thumbnailUrl,
              initialVibeIndex: val.vibeIndex,
              vibelets: {
                val.vibeIndex: Vibe(
                  content: val,
                  vibeFolderId: val.vibeFolderId,
                  videoManager: val.mediaFormat == MediaFormatEnum.video ? videoManager : null,
                )
              },
            ))
        .toList();
    _preloadVibes(0);
  }

  Future<void> loadMoreVibes() async {
    if (isLoadingMore || !isScrollContinuable) return;
    isLoadingMore = true;

    final newVibeFolders = await authService.vibesApi.getVibes(
      limit: 6, page: vibeFolders.length ~/ 6 + 1, // TODO: Add random seed suggestions rather than pagination
    );
    if (newVibeFolders == null || newVibeFolders.vibes.isEmpty) {
      isScrollContinuable = false;
      isLoadingMore = false;
      return;
    }
    ;
    vibeFolders.addAll(newVibeFolders.vibes
        .map((val) => VibeFolder(
              vibeFolderId: val.vibeFolderId,
              thumbnailUrl: val.thumbnailUrl,
              initialVibeIndex: val.vibeIndex,
              vibelets: {
                val.vibeIndex: Vibe(
                  content: val,
                  vibeFolderId: val.vibeFolderId,
                  videoManager: val.mediaFormat == MediaFormatEnum.video ? videoManager : null,
                )
              },
            ))
        .toList());
    isLoadingMore = false;

    const maxCachedItems = 30;
    if (vibeFolders.length > maxCachedItems) {
      final itemsToRemove = vibeFolders.length - maxCachedItems;
      for (int i = 0; i < itemsToRemove; i++) {
        vibeFolders[i].dispose(); // Clean up video controllers
        // TODO: Maybe there is no need, because video manager handles it, so check it later
      }
      vibeFolders.removeRange(0, itemsToRemove);
      currentVibePage = max(0, currentVibePage - itemsToRemove);
    }

    _preloadVibes(currentVibePage);
  }

  void _preloadVibes(int centerIndex) {
    const preloadCount = 2;
    final startIndex = max(0, centerIndex - preloadCount);
    final endIndex = min(vibeFolders.length - 1, centerIndex + preloadCount);

    for (int i = startIndex; i <= endIndex; i++) {
      final vibe = vibeFolders[i].currentVibelet;
      vibe.video?.initializeVideo(cacheManager);
    }
  }

  Future<void> getPreviousVibe() async {
    final previousVibeId = currentVibelet.content.previousVibeId;
    if (previousVibeId == null) return;
    currentVibelet.video?.pause();

    if (currentVibeFolder.vibeletIds.contains(previousVibeId)) {
      currentVibeFolder.setCurrentVibeletById(previousVibeId);
      await _loadVideo();
      return;
    }

    final previousVibe = await authService.vibesApi.getByVibeId(
      previousVibeId,
    );
    if (previousVibe == null) return;

    final newVibelet = Vibe(
      content: VibeItem(
        vibeId: previousVibe.vibeId,
        vibeFolderId: previousVibe.vibeFolderId,
        mediaUrl: previousVibe.mediaUrl,
        mediaFormat: previousVibe.mediaFormat,
        thumbnailUrl: previousVibe.thumbnailUrl,
        caption: previousVibe.caption,
        creatorId: previousVibe.creatorId,
        creatorType: previousVibe.creatorType,
        creatorUsername: previousVibe.creatorUsername,
        creatorAvatarUrl: previousVibe.creatorAvatarUrl,
        iventId: previousVibe.iventId,
        iventName: previousVibe.iventName,
        dates: previousVibe.dates,
        memberCount: previousVibe.memberCount,
        memberFirstnames: previousVibe.memberFirstnames,
        likeCount: previousVibe.likeCount,
        commentCount: previousVibe.commentCount,
        nextVibeId: previousVibe.nextVibeId,
        previousVibeId: previousVibe.previousVibeId,
        vibeIndex: previousVibe.vibeIndex,
        vibeCount: previousVibe.vibeCount,
        createdAt: previousVibe.createdAt,
      ),
      vibeFolderId: previousVibe.vibeFolderId,
      videoManager: previousVibe.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );

    currentVibeFolder.addNewVibelet(newVibelet);
    await _loadVideo();
  }

  Future<void> getNextVibe() async {
    final nextVibeId = currentVibelet.content.nextVibeId;
    if (nextVibeId == null) return;
    currentVibelet.video?.pause();

    if (currentVibeFolder.vibeletIds.contains(nextVibeId)) {
      currentVibeFolder.setCurrentVibeletById(nextVibeId);
      await _loadVideo();
      return;
    }

    final nextVibe = await authService.vibesApi.getByVibeId(
      nextVibeId,
    );
    if (nextVibe == null) return;

    final newVibelet = Vibe(
      content: VibeItem(
        vibeId: nextVibe.vibeId,
        vibeFolderId: nextVibe.vibeFolderId,
        mediaUrl: nextVibe.mediaUrl,
        mediaFormat: nextVibe.mediaFormat,
        thumbnailUrl: nextVibe.thumbnailUrl,
        caption: nextVibe.caption,
        creatorId: nextVibe.creatorId,
        creatorType: nextVibe.creatorType,
        creatorUsername: nextVibe.creatorUsername,
        creatorAvatarUrl: nextVibe.creatorAvatarUrl,
        iventId: nextVibe.iventId,
        iventName: nextVibe.iventName,
        dates: nextVibe.dates,
        memberCount: nextVibe.memberCount,
        memberFirstnames: nextVibe.memberFirstnames,
        likeCount: nextVibe.likeCount,
        commentCount: nextVibe.commentCount,
        nextVibeId: nextVibe.nextVibeId,
        previousVibeId: nextVibe.previousVibeId,
        vibeIndex: nextVibe.vibeIndex,
        vibeCount: nextVibe.vibeCount,
        createdAt: nextVibe.createdAt,
      ),
      vibeFolderId: nextVibe.vibeFolderId,
      videoManager: nextVibe.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );

    currentVibeFolder.addNewVibelet(newVibelet);
    await _loadVideo();
  }

  Future<void> _loadVideo() async {
    await currentVibelet.video?.initializeVideo(cacheManager);
  }
}
