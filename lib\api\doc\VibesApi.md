# openapi.api.VibesApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createVibe**](VibesApi.md#vibescontrollercreatevibe) | **POST** /vibes/create | Vibe oluşturulur
[**deleteByVibeId**](VibesApi.md#vibescontrollerdeletebyvibeid) | **DELETE** /vibes/{id}/delete | Vibe IDsi ile vibe silinir
[**getByVibeId**](VibesApi.md#vibescontrollergetbyvibeid) | **GET** /vibes/{id} | Vibe IDsi ile vibe getirilir
[**getCommentsByVibeId**](VibesApi.md#vibescontrollergetcommentsbyvibeid) | **GET** /vibes/{id}/comments | Vibe IDsi ile vibe yorumları listelenir
[**getLikesByVibeId**](VibesApi.md#vibescontrollergetlikesbyvibeid) | **GET** /vibes/{id}/likes | Vibe IDsi ile vibe beğenileri listelenir
[**getVibes**](VibesApi.md#vibescontrollergetvibes) | **GET** /vibes | Vibe sekmesi listelenir
[**hideByVibeId**](VibesApi.md#vibescontrollerhidebyvibeid) | **POST** /vibes/{id}/hide | Vibe IDsi ile vibe gizlenir
[**likeByVibeId**](VibesApi.md#vibescontrollerlikebyvibeid) | **POST** /vibes/{id}/likes/like | Vibe IDsi ile vibe beğenilir
[**showByVibeId**](VibesApi.md#vibescontrollershowbyvibeid) | **POST** /vibes/{id}/show | Vibe IDsi ile vibe gösterime girer
[**unlikeByVibeId**](VibesApi.md#vibescontrollerunlikebyvibeid) | **POST** /vibes/{id}/likes/unlike | Vibe IDsi ile vibe beğenisi kaldırılır
[**updateByVibeId**](VibesApi.md#vibescontrollerupdatebyvibeid) | **PUT** /vibes/{id}/update | Vibe IDsi ile vibe bilgileri güncellenir


# **createVibe**
> CreateVibeReturn createVibe(createVibeDto)

Vibe oluşturulur

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final createVibeDto = CreateVibeDto(); // CreateVibeDto | 

try {
    final result = api_instance.createVibe(createVibeDto);
    print(result);
} catch (e) {
    print('Exception when calling VibesApi->createVibe: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createVibeDto** | [**CreateVibeDto**](CreateVibeDto.md)|  | 

### Return type

[**CreateVibeReturn**](CreateVibeReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteByVibeId**
> deleteByVibeId(id)

Vibe IDsi ile vibe silinir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    api_instance.deleteByVibeId(id);
} catch (e) {
    print('Exception when calling VibesApi->deleteByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getByVibeId**
> GetVibeByVibeIdReturn getByVibeId(id)

Vibe IDsi ile vibe getirilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    final result = api_instance.getByVibeId(id);
    print(result);
} catch (e) {
    print('Exception when calling VibesApi->getByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetVibeByVibeIdReturn**](GetVibeByVibeIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getCommentsByVibeId**
> GetCommentsByVibeIdReturn getCommentsByVibeId(id, limit, page)

Vibe IDsi ile vibe yorumları listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getCommentsByVibeId(id, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling VibesApi->getCommentsByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetCommentsByVibeIdReturn**](GetCommentsByVibeIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getLikesByVibeId**
> GetLikesByVibeIdReturn getLikesByVibeId(id)

Vibe IDsi ile vibe beğenileri listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    final result = api_instance.getLikesByVibeId(id);
    print(result);
} catch (e) {
    print('Exception when calling VibesApi->getLikesByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetLikesByVibeIdReturn**](GetLikesByVibeIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getVibes**
> GetVibesReturn getVibes(limit, page)

Vibe sekmesi listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getVibes(limit, page);
    print(result);
} catch (e) {
    print('Exception when calling VibesApi->getVibes: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetVibesReturn**](GetVibesReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **hideByVibeId**
> hideByVibeId(id)

Vibe IDsi ile vibe gizlenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    api_instance.hideByVibeId(id);
} catch (e) {
    print('Exception when calling VibesApi->hideByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **likeByVibeId**
> likeByVibeId(id)

Vibe IDsi ile vibe beğenilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    api_instance.likeByVibeId(id);
} catch (e) {
    print('Exception when calling VibesApi->likeByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **showByVibeId**
> showByVibeId(id)

Vibe IDsi ile vibe gösterime girer

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    api_instance.showByVibeId(id);
} catch (e) {
    print('Exception when calling VibesApi->showByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unlikeByVibeId**
> unlikeByVibeId(id)

Vibe IDsi ile vibe beğenisi kaldırılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 

try {
    api_instance.unlikeByVibeId(id);
} catch (e) {
    print('Exception when calling VibesApi->unlikeByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateByVibeId**
> updateByVibeId(id, updateByVibeIdDto)

Vibe IDsi ile vibe bilgileri güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VibesApi();
final id = id_example; // String | 
final updateByVibeIdDto = UpdateByVibeIdDto(); // UpdateByVibeIdDto | 

try {
    api_instance.updateByVibeId(id, updateByVibeIdDto);
} catch (e) {
    print('Exception when calling VibesApi->updateByVibeId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateByVibeIdDto** | [**UpdateByVibeIdDto**](UpdateByVibeIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


