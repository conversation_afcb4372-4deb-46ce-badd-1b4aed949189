import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ia_ivent_box.dart';

class MapIventBox extends StatelessWidget {
  final IventCardItem iventBanner;

  const MapIventBox({
    super.key,
    required this.iventBanner,
  });

  @override
  Widget build(BuildContext context) {
    return _buildIventBox();
  }

  /// Builds the main ivent box container with ivent details
  Widget _buildIventBox() {
    return IaIventBox(
      width: 190 * 0.87,
      height: 190,
      color: AppColors.white,
      iventId: iventBanner.iventId,
      iventName: iventBanner.iventName,
      thumbnailUrl: iventBanner.thumbnailUrl,
      locationName: iventBanner.locationName,
      creatorId: iventBanner.creatorId,
      creatorName: iventBanner.creatorUsername,
      creatorImageUrl: iventBanner.creatorImageUrl,
      isFavorited: iventBanner.isFavorited ?? false,
      onFavorite: () {},
      onShare: () {},
    );
  }
}
