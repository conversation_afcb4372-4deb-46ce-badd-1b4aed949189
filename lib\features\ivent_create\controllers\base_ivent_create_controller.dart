import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

abstract class BaseIventCreateController extends BaseController {
  final IventCreateStateManager state;

  BaseIventCreateController(AuthService authService, this.state) : super(authService);
}
