import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ia_ivent_grid.dart';
import 'package:ivent_app/features/home/<USER>/feed_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_screen_date_picker.dart';

class FeedScreen extends StatefulWidget {
  const FeedScreen({super.key});

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  late final HomeController _controller;

  FeedController get _feedController => _controller.feedController;

  @override
  void initState() {
    super.initState();
    _controller = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            const SizedBox(height: AppDimensions.padding8),
            _buildTopBar(),
            FeedScreenDatePicker(
              onDateRangeSelected: _controller.feedController.toggleSelectedDates,
              onDateButtonPressed: _controller.feedController.toggleTimeFilter,
            ),
            Expanded(child: _buildFeed()),
          ],
        ),
        if (_controller.sessionUser.sessionRole == UserTypeEnum.CREATOR)
          Positioned(
            right: AppDimensions.padding20,
            bottom: AppDimensions.buttonSizeCreateIventL + AppDimensions.padding20,
            child: HomeButtons.createIventButtonL(),
          ),
      ],
    );
  }

  Padding _buildFeed() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Obx(() {
        final ivents = _feedController.iventItems;
        return IaSearchPlaceholder(
          entityType: 'iVent',
          isSearching: _feedController.isSearching,
          isQueryEmpty: false,
          isResultsEmpty: ivents.isEmpty,
          isDefaultStateEmpty: false,
          builder: (context) {
            return IaIventGrid(
              iventItems: ivents,
              onLoadMore: _feedController.loadNewIventItems,
              onRefresh: _feedController.retrieveIventList,
            );
          },
        );
      }),
    );
  }

  Padding _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        children: [
          _buildIventLogo(),
          const Spacer(),
          HomeButtons.searchButton(
            onTap: _controller.homePanelsController.goToSearchPage,
          ),
          const SizedBox(width: 20),
          HomeButtons.filterButton(
            isActive: _controller.state.appliedFilterCount != 0,
            filterCount: _controller.state.appliedFilterCount,
            onTap: _controller.homePanelsController.goToFilterPage,
          ),
        ],
      ),
    );
  }

  Row _buildIventLogo() {
    return Row(
      children: [
        Text('iVent', style: AppTextStyles.size20Bold),
        Text('Keşfet', style: AppTextStyles.size20BoldPrimary),
      ],
    );
  }
}
