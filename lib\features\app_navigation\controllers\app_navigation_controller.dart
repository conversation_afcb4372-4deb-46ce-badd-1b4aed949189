import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class AppNavigationController extends BaseController {
  final HomeStateManager homeStateManager;
  final ProfileStateManager profileStateManager;

  AppNavigationController(
    AuthService authService,
    this.homeStateManager,
    this.profileStateManager,
  ) : super(authService);

  @override
  Future<void> initController() async {
    super.initController();
    Get.put(HomeController(authService, homeStateManager));
    // Get.put(VibesPageController(authService));
    // Get.put(ProfileController(authService, profileStateManager), tag: sessionUser.sessionId);
    print('BottomNavBarController has been initialized with user: ${sessionUser.sessionId}');
  }
}
