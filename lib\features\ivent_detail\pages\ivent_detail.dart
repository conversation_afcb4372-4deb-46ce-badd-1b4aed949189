import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/utils/list_utils.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_linked_avatars.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_text_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_divider.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ia_ivent_thumbnail.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_detail_buttons.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/widgets/tags/ivent_tags_scroll.dart';
import 'package:ivent_app/features/ivent_detail/widgets/tiles/contact_tile.dart';

/// iVent detail page displaying comprehensive information about an iVent
///
/// Shows iVent details including thumbnail, description, tags, collaborators,
/// participants, and action buttons. The page adapts its content based on
/// the user's relationship to the iVent (creator, participant, or visitor).
///
/// The page uses reactive state management through GetX and follows the
/// project's established patterns for UI composition and styling.

class IventDetail extends StatefulWidget {
  /// The unique identifier for the iVent
  final String iventId;

  const IventDetail(this.iventId, {Key? key}) : super(key: key);

  @override
  State<IventDetail> createState() => _IventDetailState();
}

class _IventDetailState extends State<IventDetail> {
  // Controllers
  late final IventDetailsController _controller;

  // State
  bool isDescriptionExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.iventId);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final pageContext = _controller.iventInfoController.iventPage;
      if (pageContext == null) return IaScaffold.loading();

      return IaScaffold.noSearch(
        showDivider: false,
        title: 'iVent Detay',
        trailing: _buildMembersButton(),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: _buildContent(pageContext),
              ),
            ),
            _buildBottomBar(pageContext),
          ],
        ),
      );
    });
  }

  // Widget builders

  /// Builds the members button in the app bar
  Widget _buildMembersButton() {
    return IaTextButton(
      text: 'Üyeler',
      textStyle: AppTextStyles.size16Regular,
    );
  }

  /// Builds the main content of the page
  Widget _buildContent(GetIventPageByIventIdReturn pageContext) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppDimensions.padding12),
        _buildIventThumbnail(pageContext),
        _buildTagsSection(pageContext),
        _buildCreatorAndParticipantsSection(pageContext),
        _buildDescription(pageContext),
      ],
    );
  }

  /// Builds the iVent thumbnail section
  Widget _buildIventThumbnail(GetIventPageByIventIdReturn pageContext) {
    return IaIventThumbnail.big(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      iventId: pageContext.iventId,
      iventName: pageContext.iventName,
      locationId: pageContext.locationId,
      locationName: pageContext.locationName,
      date: pageContext.dates.map((date) => DateTime.parse(date)).toList(),
      thumbnailUrl: pageContext.thumbnailUrl,
      isFavorited: _controller.iventInfoController.isFavorited,
      onFavorite: _controller.iventInfoController.toggleIventFavorite,
      onShare: () {}, // TODO: Implement share functionality
      favoriteCount: pageContext.favoriteCount,
    );
  }

  /// Builds the tags section if tags exist
  Widget _buildTagsSection(GetIventPageByIventIdReturn pageContext) {
    if (pageContext.tagNames.isEmpty) {
      return const SizedBox.shrink();
    }

    return IventTagsScroll(
      margin: const EdgeInsets.only(top: AppDimensions.padding12),
      tags: pageContext.tagNames,
    );
  }

  /// Builds the creator and participants section
  Widget _buildCreatorAndParticipantsSection(GetIventPageByIventIdReturn pageContext) {
    return IaListTile.withImageUrl(
      margin: const EdgeInsets.all(AppDimensions.padding20),
      onTap: () => _controller.goToCollabsPage(),
      avatarUrl: pageContext.creatorImageUrl,
      title: pageContext.creatorUsername,
      subtitle: _buildCollabsDetailsText(
        collabCount: pageContext.collabCount,
        collabNames: pageContext.collabNames,
      ),
      trailing: IaLinkedAvatars(
        maxWidth: Get.width * 0.3,
        onTap: () => _controller.goToParticipantsPage(
          pageContext.viewType,
          pageContext.memberCount,
        ),
        viewType: pageContext.viewType,
        memberAvatarUrls: pageContext.memberAvatarUrls,
        memberCount: pageContext.memberCount,
        memberNames: pageContext.memberFirstnames ?? [],
      ),
    );
  }

  String? _buildCollabsDetailsText({
    required int collabCount,
    required List<String> collabNames,
  }) {
    if (collabCount == 0)
      return null;
    else if (collabCount == 1)
      return '${collabNames[0]}';
    else if (collabCount == 2)
      return '${collabNames[0]}, ${collabNames[1]}';
    else
      return '${collabNames[0]}, ${collabNames[1]} ve ${collabCount - 2} Diğer Paydaş';
  }

  Widget _buildDescription(GetIventPageByIventIdReturn pageContext) {
    return pageContext.viewType != IventViewTypeEnum.created
        ? _buildNormalDescription(pageContext)
        : _buildCreatorDescription(pageContext);
  }

  Widget _buildNormalDescription(GetIventPageByIventIdReturn pageContext) {
    if (pageContext.description == null) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Açıklama', style: AppTextStyles.size16Bold),
          const SizedBox(height: AppDimensions.padding4),
          Text(
            pageContext.description!
                    .substring(0, isDescriptionExpanded ? null : min(500, pageContext.description!.length)) +
                (pageContext.description!.length > 500 ? (isDescriptionExpanded ? '' : '...') : ''),
            style: AppTextStyles.size16RegularTextSecondary,
            maxLines: null,
            overflow: TextOverflow.visible,
          ),
          if (pageContext.description!.length > 500)
            IaTextButton(
              margin: const EdgeInsets.only(top: AppDimensions.padding4),
              onPressed: () => setState(() => isDescriptionExpanded = !isDescriptionExpanded),
              text: isDescriptionExpanded ? 'Gizle' : 'Devamını Oku',
              textStyle: AppTextStyles.size16RegularPrimary,
            ),
          const SizedBox(height: AppDimensions.padding20),
        ],
      ),
    );
  }

  Widget _buildCreatorDescription(GetIventPageByIventIdReturn pageContext) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: GestureDetector(
        onTap: () {},
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Açıklama', style: AppTextStyles.size16Bold),
            const SizedBox(height: AppDimensions.padding8),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const IaSvgIcon(iconPath: AppAssets.editPencilLine01, iconColor: AppColors.darkGrey),
                const SizedBox(width: AppDimensions.padding8),
                Expanded(
                  child: Text(
                    pageContext.description ?? '',
                    style: AppTextStyles.size16RegularTextSecondary,
                    maxLines: 1,
                    softWrap: false,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.padding20),
            _buildRegistrationTypes(pageContext),
          ],
        ),
      ),
    );
  }

  Widget _buildRegistrationTypes(GetIventPageByIventIdReturn pageContext) {
    if (pageContext.viewType != IventViewTypeEnum.created) return const SizedBox.shrink();
    final registrationTypes = [
      pageContext.googleFormsUrl,
      pageContext.instagramUsername,
      pageContext.whatsappUrl,
      pageContext.whatsappNumber,
      pageContext.callNumber,
      pageContext.websiteUrl,
    ];
    if (registrationTypes.where((var element) => element != null && element.isNotEmpty).isEmpty)
      return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Kayıt Türleri', style: AppTextStyles.size16Bold),
        const SizedBox(height: AppDimensions.padding8),
        ...insertBetween(
          [
            if (pageContext.googleFormsUrl != null && pageContext.googleFormsUrl!.isNotEmpty)
              ContactTile.googleForms(text: pageContext.googleFormsUrl!),
            if (pageContext.instagramUsername != null && pageContext.instagramUsername!.isNotEmpty)
              ContactTile.instagram(text: pageContext.instagramUsername!),
            if (pageContext.whatsappUrl != null && pageContext.whatsappUrl!.isNotEmpty)
              ContactTile.whatsappGroup(text: pageContext.whatsappUrl!),
            if (pageContext.whatsappNumber != null && pageContext.whatsappNumber!.isNotEmpty)
              ContactTile.whatsappMessage(text: pageContext.whatsappNumber!),
            if (pageContext.callNumber != null && pageContext.callNumber!.isNotEmpty)
              ContactTile.phoneCall(text: pageContext.callNumber!),
            if (pageContext.websiteUrl != null && pageContext.websiteUrl!.isNotEmpty)
              ContactTile.website(text: pageContext.websiteUrl!),
          ],
          const SizedBox(height: AppDimensions.padding12),
        ),
        const SizedBox(height: AppDimensions.padding20),
      ],
    );
  }

  /// Builds the bottom action bar based on user's relationship to the iVent
  Widget _buildBottomBar(GetIventPageByIventIdReturn pageContext) {
    return Column(
      children: [
        const IaDivider(),
        Container(
          height: AppDimensions.bottomIventDetayBottomBarHeight,
          padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
          child: _buildBottomBarContent(pageContext),
        ),
      ],
    );
  }

  /// Builds the appropriate bottom bar content based on view type
  Widget _buildBottomBarContent(GetIventPageByIventIdReturn pageContext) {
    switch (pageContext.viewType) {
      case IventViewTypeEnum.joined:
        return _buildJoinedViewBottomBar(pageContext);
      case IventViewTypeEnum.created:
        return _buildCreatedViewBottomBar();
      default:
        return _buildOtherViewBottomBar();
    }
  }

  /// Builds bottom bar for joined users showing communication options
  Widget _buildJoinedViewBottomBar(GetIventPageByIventIdReturn pageContext) {
    return Row(
      children: insertBetween(
        [
          if (pageContext.googleFormsUrl != null) IventDetailButtons.iventDetailCommunicationGoogleForms(onTap: () {}),
          if (pageContext.instagramUsername != null) IventDetailButtons.iventDetailCommunicationInstagram(onTap: () {}),
          if (pageContext.whatsappUrl != null) IventDetailButtons.iventDetailCommunicationWhatsappGroup(onTap: () {}),
          if (pageContext.whatsappNumber != null) IventDetailButtons.iventDetailCommunicationWhatsappChat(onTap: () {}),
          if (pageContext.callNumber != null) IventDetailButtons.iventDetailCommunicationCall(onTap: () {}),
          if (pageContext.websiteUrl != null) IventDetailButtons.iventDetailCommunicationLink(onTap: () {}),
        ],
        const SizedBox(width: AppDimensions.padding16),
      ),
    );
  }

  /// Builds bottom bar for iVent creators showing share option
  Widget _buildCreatedViewBottomBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: SharedButtons.longBar(
        isEnabled: true,
        onTap: () {}, // TODO: Implement share functionality
        text: 'Etkinliği Paylaş',
        trailingIconPath: AppAssets.shareAndroid,
      ),
    );
  }

  /// Builds bottom bar for other users showing join option
  Widget _buildOtherViewBottomBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Katılıyor musun?', style: AppTextStyles.size16Bold),
        IventDetailButtons.iventDetailJoin(
          onTap: () => _controller.goToInitiallyInvitableUsersPage(),
        ),
      ],
    );
  }
}
