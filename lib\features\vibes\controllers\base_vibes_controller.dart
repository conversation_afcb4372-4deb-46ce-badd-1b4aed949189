import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

abstract class BaseVibesController extends BaseController {
  // Common properties
  final VideoManager videoManager = VideoManager();
  final CacheManager cacheManager = CacheManager(
    Config(
      'vibeCache',
      stalePeriod: const Duration(hours: 1),
      maxNrOfCacheObjects: 100,
    ),
  );

  BaseVibesController(AuthService authService) : super(authService);
}
