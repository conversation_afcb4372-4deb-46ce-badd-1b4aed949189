import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_basic_info_tile.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';

class VibeUpload extends StatefulWidget {
  const VibeUpload({super.key});

  @override
  State<VibeUpload> createState() => _VibeUploadState();
}

class _VibeUploadState extends State<VibeUpload> {
  final VibeUploadController _controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return IaScaffold.empty(
      body: Stack(
        children: [
          Positioned.fill(
            child: Container(
              color: AppColors.black,
              child: Center(
                child: Obx(() {
                  return Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(_controller.isFrontCamera ? pi : 0),
                    child: Image.file(
                      File(_controller.capturedImagePath),
                      fit: BoxFit.contain,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  );
                }),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    child: Obx(() {
                      final sessionUser = _controller.sessionUser;
                      return IaBasicInfoTile.withImageUrl(
                        avatarUrl: sessionUser.sessionAvatarUrl,
                        avatarSize: 20,
                        title: '@${sessionUser.sessionUsername}',
                        // titleStyle: AppTextStyles.size14Bold,
                        subtitle: 'Açıklamayı Düzenle',
                        // subtitleStyle: AppTextStyles.size14Bold,
                      );
                    }),
                  ),
                  Row(
                    children: [
                      IaRoundedContainer(
                        color: AppColors.grey700,
                        padding: const EdgeInsets.all(10),
                        child: const Icon(Icons.download_outlined, color: AppColors.white, size: 16),
                        onTap: () async {
                          final sourceFile = File(_controller.capturedImagePath);
                          final String destinationPath =
                              '/storage/emulated/0/Download/Ivent_${DateTime.now().millisecondsSinceEpoch}.jpg';
                          await sourceFile.copy(destinationPath);
                        },
                      ),
                      const SizedBox(width: 4),
                      const IaRoundedContainer(
                        color: AppColors.grey700,
                        padding: EdgeInsets.all(10),
                        child: Text('Memories Ekle'),
                      ),
                      const SizedBox(width: 4),
                      const IaRoundedContainer(
                        color: AppColors.grey700,
                        padding: EdgeInsets.all(10),
                        child: Text('Vibes Paylaş'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
