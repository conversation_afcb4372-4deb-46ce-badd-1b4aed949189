import 'package:get/get.dart';
import 'package:ivent_app/app/routes/ivent_create.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/base_ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_submission_controller.dart';

class IventCreateController extends BaseIventCreateController {
  IventCreateController(
    AuthService authService,
    IventCreateStateManager state,
  ) : super(authService, state);

  late final IventCreateMapController mapController;
  late final IventCreateFormController formController;
  late final IventCreateImageController imageController;
  late final IventCreateSubmissionController submissionController;

  final _isInitializationComplete = false.obs;

  bool get isInitializationComplete => _isInitializationComplete.value;

  set isInitializationComplete(bool value) => _isInitializationComplete.value = value;

  @override
  void initController() async {
    super.initController();
    await _initializeChildControllers();
    isInitializationComplete = true;
    print('IventCreateController has been initialized with user: ${sessionUser.sessionId}');
  }

  @override
  void closeController() {
    _disposeChildControllers();
    super.closeController();
  }

  void goToMainCategoryPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_KATEGORI_SECINIZ);
  }

  void goToSubCategoryPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_ALT_KATEGORI_SECINIZ);
  }

  void goToImageSelectionPage() {
    imageController.getSuggestedImagesPage();
  }

  void goToImageGalleryPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_IMAGE_GALLERY);
  }

  void goToDateSelectionPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_DATE_SELECTION);
  }

  void goToMapPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_MAP);
  }

  void goToPreviewPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_PREVIEW);
  }

  void goToDescriptionPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_ACIKLAMA);
  }

  void goToTagsPage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_TAGS);
  }

  void goToRegisterTypePage() {
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_KAYIT_TURU_SECINIZ);
  }

  Future<void> _initializeChildControllers() async {
    mapController = Get.put(IventCreateMapController(authService, state));
    formController = Get.put(IventCreateFormController(authService, state));
    imageController = Get.put(IventCreateImageController(authService, state));
    submissionController = Get.put(IventCreateSubmissionController(authService, state));
  }

  void _disposeChildControllers() {
    Get.delete<IventCreateMapController>();
    Get.delete<IventCreateFormController>();
    Get.delete<IventCreateImageController>();
    Get.delete<IventCreateSubmissionController>();
  }
}
