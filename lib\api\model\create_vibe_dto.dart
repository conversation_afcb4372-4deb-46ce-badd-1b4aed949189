//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateVibeDto {
  /// Returns a new [CreateVibeDto] instance.
  CreateVibeDto({
    this.fileBuffer,
    this.mediaUrl,
    required this.mediaFormat,
    this.caption,
    required this.squadId,
    required this.privacy,
  });

  /// Base64 encoded file buffer for media upload
  String? fileBuffer;

  /// URL to the media file if hosted externally
  String? mediaUrl;

  MediaFormatEnum mediaFormat;

  /// Caption or description for the vibe
  String? caption;

  /// UUID of the squad this vibe belongs to
  String squadId;

  VibePrivacyEnum privacy;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateVibeDto &&
    other.fileBuffer == fileBuffer &&
    other.mediaUrl == mediaUrl &&
    other.mediaFormat == mediaFormat &&
    other.caption == caption &&
    other.squadId == squadId &&
    other.privacy == privacy;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (fileBuffer == null ? 0 : fileBuffer!.hashCode) +
    (mediaUrl == null ? 0 : mediaUrl!.hashCode) +
    (mediaFormat.hashCode) +
    (caption == null ? 0 : caption!.hashCode) +
    (squadId.hashCode) +
    (privacy.hashCode);

  @override
  String toString() => 'CreateVibeDto[fileBuffer=$fileBuffer, mediaUrl=$mediaUrl, mediaFormat=$mediaFormat, caption=$caption, squadId=$squadId, privacy=$privacy]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.fileBuffer != null) {
      json[r'fileBuffer'] = this.fileBuffer;
    } else {
      json[r'fileBuffer'] = null;
    }
    if (this.mediaUrl != null) {
      json[r'mediaUrl'] = this.mediaUrl;
    } else {
      json[r'mediaUrl'] = null;
    }
      json[r'mediaFormat'] = this.mediaFormat;
    if (this.caption != null) {
      json[r'caption'] = this.caption;
    } else {
      json[r'caption'] = null;
    }
      json[r'squadId'] = this.squadId;
      json[r'privacy'] = this.privacy;
    return json;
  }

  /// Returns a new [CreateVibeDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateVibeDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateVibeDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateVibeDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateVibeDto(
        fileBuffer: mapValueOfType<String>(json, r'fileBuffer'),
        mediaUrl: mapValueOfType<String>(json, r'mediaUrl'),
        mediaFormat: MediaFormatEnum.fromJson(json[r'mediaFormat'])!,
        caption: mapValueOfType<String>(json, r'caption'),
        squadId: mapValueOfType<String>(json, r'squadId')!,
        privacy: VibePrivacyEnum.fromJson(json[r'privacy'])!,
      );
    }
    return null;
  }

  static List<CreateVibeDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateVibeDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateVibeDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateVibeDto> mapFromJson(dynamic json) {
    final map = <String, CreateVibeDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateVibeDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateVibeDto-objects as value to a dart map
  static Map<String, List<CreateVibeDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateVibeDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateVibeDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'mediaFormat',
    'squadId',
    'privacy',
  };
}

