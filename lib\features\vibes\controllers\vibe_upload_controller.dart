import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';

class VibeUploadController extends BaseVibesController {
  VibeUploadController(AuthService authService) : super(authService);

  final _capturedImagePath = ''.obs;
  final _isFrontCamera = false.obs;

  String get capturedImagePath => _capturedImagePath.value;
  bool get isFrontCamera => _isFrontCamera.value;

  set capturedImagePath(String value) => _capturedImagePath.value = value;
  set isFrontCamera(bool value) => _isFrontCamera.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    print('VibeUploadController has been initialized with user: ${sessionUser.sessionId}');
  }
}
