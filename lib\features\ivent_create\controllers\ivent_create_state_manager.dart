import 'dart:typed_data';

import 'package:get/get.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';

class IventCreateStateManager extends GetxController {
  final _selectedImageUrl = Rxn<String>();
  final _selectedImageFile = Rxn<Uint8List>();
  final _selectedPlace = Rxn<IaLocationItem>();
  
  String? get selectedImageUrl => _selectedImageUrl.value;
  Uint8List? get selectedImageFile => _selectedImageFile.value;
  IaLocationItem? get selectedPlace => _selectedPlace.value;

  set selectedImageUrl(String? value) => _selectedImageUrl.value = value;
  set selectedImageFile(Uint8List? value) => _selectedImageFile.value = value;
  set selectedPlace(IaLocationItem? value) => _selectedPlace.value = value;
}
