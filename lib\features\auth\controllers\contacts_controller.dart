import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/auth.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/features/auth/controllers/base_auth_controller.dart';

class ContactsController extends BaseAuthController {
  ContactsController(AuthService authService, AuthStateManager state) : super(authService, state);
  final _pendingContactIds = <String>[].obs;
  final _getContactsReturn = Rxn<GetContactsByUserIdReturn>();

  List<String> get pendingContactIds => _pendingContactIds;
  GetContactsByUserIdReturn? get getContactsReturn => _getContactsReturn.value;
  bool get hasContactsLoaded => getContactsReturn != null;
  int get pendingInvitationsCount => _pendingContactIds.length;

  set pendingContactIds(List<String> value) => _pendingContactIds.assignAll(value);
  set getContactsReturn(GetContactsByUserIdReturn? value) => _getContactsReturn.value = value;

  Future<void> goToContactsPage(bool accessGranted) async {
    try {
      Get.toNamed(AuthRoutes.CONTACTS_PAGE, arguments: accessGranted);

      if (accessGranted) {
        await _loadContacts();
      }
    } catch (error) {
      handleAuthError(error, 'Kişiler sayfasına geçiş sırasında hata oluştu');
    }
  }

  Future<void> toggleFriendRequest(String userId) async {
    try {
      setLoadingState(true);

      if (pendingContactIds.contains(userId)) {
        await _cancelFriendRequest(userId);
      } else {
        await _sendFriendRequest(userId);
      }

      setLoadingState(false);
    } catch (error) {
      handleAuthError(error, 'Arkadaşlık isteği sırasında hata oluştu');
    }
  }

  UserRelationshipStatusEnum? getRelationshipStatus(String userId) {
    if (pendingContactIds.contains(userId)) {
      return UserRelationshipStatusEnum.pending;
    }
    return null;
  }

  void clearContactsData() {
    _pendingContactIds.clear();
    _getContactsReturn.value = null;
    state.clearError();
  }

  Future<void> _loadContacts() async {
    try {
      setLoadingState(true);

      // TODO: Replace with actual device contacts
      final demoPhoneNumbers = [
        '+90(836)8415046',
        '+90(770)1229609',
        '+90(705)5095422',
      ];

      getContactsReturn = await authService.usersApi.getContactsByUserId(
        sessionUser.sessionId,
        GetContactsByUserIdDto(phoneNumbers: demoPhoneNumbers),
        limit: 10,
        page: 1,
      );

      setLoadingState(false);
    } catch (error) {
      handleAuthError(error, 'Kişiler yüklenirken hata oluştu');
    }
  }

  Future<void> _sendFriendRequest(String userId) async {
    await authService.userRelationshipsApi.inviteFriendByUserId(userId);

    _pendingContactIds.add(userId);
  }

  Future<void> _cancelFriendRequest(String userId) async {
    await authService.userRelationshipsApi.uninviteFriendByUserId(userId);

    _pendingContactIds.remove(userId);
  }
}
