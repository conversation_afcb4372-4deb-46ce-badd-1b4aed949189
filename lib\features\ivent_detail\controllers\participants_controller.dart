import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/ivent_detail.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';

class ParticipantsController extends BaseIventDetailsController {
  ParticipantsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  final _participants = Rxn<SearchParticipantsByIventIdReturn>();

  SearchParticipantsByIventIdReturn? get participants => _participants.value;

  set participants(SearchParticipantsByIventIdReturn? value) => _participants.value = value;

  Future<void> getParticipantsPage(IventViewTypeEnum viewType, int participantCount, {String? q}) async {
    if (viewType == IventViewTypeEnum.default_ && participantCount == 0) return;

    participants = await authService.squadMembershipsApi.searchParticipantsByIventId(
      iventId,
      q: q,
    );

    if (viewType == IventViewTypeEnum.joined) {
      Get.toNamed(IventDetayRoutes.IVENT_DETAY_SQUAD, arguments: iventId);
    } else if (viewType == IventViewTypeEnum.created) {
      Get.toNamed(IventDetayRoutes.IVENT_DETAY_KISILER, arguments: iventId);
    } else {
      Get.toNamed(IventDetayRoutes.IVENT_DETAY_KISILER, arguments: iventId);
    }
  }
}
