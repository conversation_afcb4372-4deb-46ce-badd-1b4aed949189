import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';

class IventInfoController extends BaseIventDetailsController {
  IventInfoController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  final _iventPage = Rxn<GetIventPageByIventIdReturn>();
  final _isFavorited = false.obs;

  GetIventPageByIventIdReturn? get iventPage => _iventPage.value;
  bool get isFavorited => _isFavorited.value;

  set iventPage(GetIventPageByIventIdReturn? value) => _iventPage.value = value;
  set isFavorited(bool value) => _isFavorited.value = value;

  @override
  void initController() async {
    super.initController();
    await _loadIventData();
  }

  Future<void> _loadIventData() async {
    try {
      iventPage = await authService.iventsApi.getIventPageByIventId(
        iventId,
      );

      if (iventPage != null) {
        isFavorited = iventPage!.isFavorited ?? false;
      }
    } catch (e) {
      // Handle error appropriately - could show error message or retry
      // For now, we'll let the UI handle the null state
    }
  }

  Future<void> toggleIventFavorite() async {
    try {
      if (isFavorited) {
        await authService.iventsApi.unfavoriteIventByIventId(
          iventId,
        );
      } else {
        await authService.iventsApi.favoriteIventByIventId(
          iventId,
        );
      }

      isFavorited = !isFavorited;
    } catch (e) {
      // Handle error appropriately - could show error message
      // For now, we'll keep the current state unchanged
    }
  }

  Future<void> refreshIventData() async {
    await _loadIventData();
  }
}
