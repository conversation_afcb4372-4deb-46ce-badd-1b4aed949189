# openapi.model.CreateVibeDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**fileBuffer** | **String** | Base64 encoded file buffer for media upload | [optional] 
**mediaUrl** | **String** | URL to the media file if hosted externally | [optional] 
**mediaFormat** | [**MediaFormatEnum**](MediaFormatEnum.md) |  | 
**caption** | **String** | Caption or description for the vibe | [optional] 
**squadId** | **String** | UUID of the squad this vibe belongs to | 
**privacy** | [**VibePrivacyEnum**](VibePrivacyEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



