import 'dart:async';

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/base_ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IventCreateMapController extends BaseIventCreateController {
  IventCreateMapController(AuthService authService, IventCreateStateManager state) : super(authService, state) {
    mapboxController = MapboxController(authService: authService);
  }

  late final BaseSearchBarController baseSearchBarController;
  late final MapboxController mapboxController;

  final _markers = <PointAnnotationOptions>[].obs;
  final _panelController = PanelController();
  final _searchSuggestions = <SearchBoxSuggestFeature>[].obs;

  List<PointAnnotationOptions> get markers => _markers;
  PanelController get panelController => _panelController;
  List<SearchBoxSuggestFeature> get searchSuggestions => _searchSuggestions;

  set markers(List<PointAnnotationOptions> value) => _markers.assignAll(value);

  @override
  void initController() async {
    super.initController();
    baseSearchBarController = Get.put(BaseSearchBarController(_searchPlaces));
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>();
    super.closeController();
  }

  Future<void> _searchPlaces(String? query) async {
    if (query == null || query.isEmpty) {
      searchSuggestions.clear();
      return;
    }
    final result = await authService.mapboxApi.searchBoxSuggest(
      query,
      sessionUser.sessionId,
      limit: 10,
      proximity: mapboxController.userLocation != null
          ? '${mapboxController.userLocationCoordinates!.lng},${mapboxController.userLocationCoordinates!.lat}'
          : null,
      types: 'country,region,postcode,district,place,city,locality,neighborhood,street,address,poi,category',
    );
    if (result != null) {
      searchSuggestions.assignAll(result.suggestions);
    }
  }

  Future<void> selectPlace(SearchBoxSuggestFeature suggestion) async {
    final result = await authService.mapboxApi.searchBoxRetrieve(
      suggestion.mapboxId,
      sessionUser.sessionId,
    );

    if (result != null) {
      state.selectedPlace = IaLocationItem.fromProperties(result.features[0].properties);
      panelController.close();
      baseSearchBarController.text = suggestion.name;
      searchSuggestions.clear();
      mapboxController.markerController.removeMarkers(['selected-place']);
      mapboxController.markerController.addMarkers([
        MarkerFeature(
          id: 'selected-place',
          properties: MarkerFeatureProperties(isSelected: false),
          geometry: MarkerFeatureGeometry(coordinates: [state.selectedPlace!.longitude, state.selectedPlace!.latitude]),
        )
      ]);
      mapboxController.mapboxMap.flyTo(
        CameraOptions(
          center: Point(coordinates: Position(state.selectedPlace!.longitude, state.selectedPlace!.latitude)),
          zoom: 14,
        ),
        MapAnimationOptions(duration: 50),
      );
    }
  }

  void clearSearch() {
    baseSearchBarController.clearSearch();
    searchSuggestions.clear();
    state.selectedPlace = null;
    markers = [];
  }
}
